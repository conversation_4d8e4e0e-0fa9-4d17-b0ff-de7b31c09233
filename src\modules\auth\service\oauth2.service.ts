import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '@modules/user/entities';
import { UserService } from '@/modules/user/user/service/user.service';
import { AppException } from '@/common';
import { AUTH_ERROR_CODE } from '../errors';
import { HttpService } from '@nestjs/axios';
import { lastValueFrom } from 'rxjs';
import { GoogleApiService } from '@shared/services/google/google-api.service';
import { ZaloSocialService } from '@shared/services/zalo/zalo-social.service';
import { UserRoleService } from '@/modules/user/user/service/user-role.service';
import { EmailPlaceholderService } from '@/modules/email/services/email-placeholder.service';
import { RagApiKeyProvisioningService } from '@/modules/user/user/service/rag-api-key-provisioning.service';
import { Transactional } from 'typeorm-transactional';

interface GoogleUserInfo {
  id: string;
  email: string;
  verified_email: boolean;
  name: string;
  given_name: string;
  family_name: string;
  picture: string;
  locale: string;
}

interface FacebookUserInfo {
  id: string;
  email?: string;
  name?: string;
  first_name?: string;
  last_name?: string;
  picture?: {
    data: {
      url: string;
    };
  };
}

interface ZaloUserInfo {
  id: string;
  name: string;
  picture?: {
    data: {
      url: string;
    };
  };
  gender?: string;
  birthday?: string;
}

@Injectable()
export class OAuth2Service {
  private readonly logger = new Logger(OAuth2Service.name);
  private readonly facebookAppId: string | undefined;
  private readonly facebookAppSecret: string | undefined;
  private readonly facebookRedirectUri: string | undefined;

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
    private readonly googleApiService: GoogleApiService,
    private readonly zaloSocialService: ZaloSocialService,
    private readonly userService: UserService,
    private readonly userRoleService: UserRoleService,
    private readonly emailPlaceholderService: EmailPlaceholderService,
    private readonly ragApiKeyProvisioningService: RagApiKeyProvisioningService,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {
    this.facebookAppId = this.configService.get<string>('FACEBOOK_APP_ID');
    this.facebookAppSecret = this.configService.get<string>(
      'FACEBOOK_APP_SECRET',
    );
    this.facebookRedirectUri = this.configService.get<string>(
      'FACEBOOK_REDIRECT_URI',
    );
  }

  /**
   * Tạo URL xác thực Google OAuth2
   * @returns URL xác thực Google
   */
  generateGoogleAuthUrl(redirectUri?: string): string {
    const scopes = [
      'https://www.googleapis.com/auth/userinfo.profile',
      'https://www.googleapis.com/auth/userinfo.email',
    ];
    return this.googleApiService.generateAuthUrl(
      scopes,
      undefined,
      redirectUri,
    );
  }

  /**
   * Tạo URL xác thực Facebook OAuth2
   * @returns Object chứa URL, scope và state
   */
  generateFacebookAuthUrl(redirectUri?: string): {
    url: string;
    scope: string;
    state: string;
  } {
    const actualRedirectUri = redirectUri || this.facebookRedirectUri;
    if (!this.facebookAppId || !actualRedirectUri) {
      throw new AppException(
        AUTH_ERROR_CODE.SOCIAL_AUTH_FAILED,
        'Thiếu cấu hình Facebook App ID hoặc Redirect URI',
      );
    }

    const scopes = ['email', 'public_profile', 'pages_show_list'];
    const scopeString = scopes.join(',');
    const state = 'facebook';

    const url = `https://www.facebook.com/v18.0/dialog/oauth?client_id=${this.facebookAppId}&redirect_uri=${encodeURIComponent(actualRedirectUri)}&scope=${scopeString}&state=${state}`;

    return {
      url,
      scope: scopeString,
      state,
    };
  }

  /**
   * Tạo URL xác thực Zalo OAuth2
   * @returns URL xác thực Zalo
   */
  generateZaloAuthUrl(redirectUri?: string): string {
    const zaloAppId = this.configService.get<string>('ZALO_APP_ID');
    if (!zaloAppId) {
      throw new AppException(
        AUTH_ERROR_CODE.SOCIAL_AUTH_FAILED,
        'Thiếu cấu hình Zalo App ID',
      );
    }

    const actualRedirectUri =
      redirectUri || this.configService.get<string>('ZALO_REDIRECT_URI');
    if (!actualRedirectUri) {
      throw new AppException(
        AUTH_ERROR_CODE.SOCIAL_AUTH_FAILED,
        'Thiếu cấu hình Zalo Redirect URI',
      );
    }

    // Tạo state token để bảo mật (bắt buộc theo Zalo API V4)
    const state = `zalo_auth_${Date.now()}_${Math.random().toString(36).substring(2)}`;

    // Tạo URL với nocache parameter
    const baseUrl = this.zaloSocialService.createAuthUrl(
      zaloAppId,
      actualRedirectUri,
      'id,name,picture',
      state,
    );
    const nocacheParam = `nocache=${Date.now()}`;

    // Thêm nocache parameter vào URL
    const separator = baseUrl.includes('?') ? '&' : '?';
    return `${baseUrl}${separator}${nocacheParam}`;
  }

  /**
   * Xử lý đăng nhập Google OAuth2
   * @param code Authorization code từ Google
   * @param redirectUri URL chuyển hướng (tùy chọn)
   * @param ref Mã người giới thiệu (tùy chọn)
   * @returns Thông tin người dùng và token
   */
  @Transactional()
  async handleGoogleLogin(
    code: string,
    redirectUri?: string,
    ref?: number,
  ): Promise<User> {
    try {
      // Đổi code lấy token
      const tokens = await this.googleApiService.getToken(code, redirectUri);
      this.googleApiService.setCredentials(tokens);

      // Lấy thông tin người dùng
      const userInfo =
        (await this.googleApiService.getUserInfo()) as GoogleUserInfo;

      if (!userInfo || !userInfo.id) {
        throw new AppException(
          AUTH_ERROR_CODE.SOCIAL_AUTH_FAILED,
          'Không thể lấy thông tin người dùng từ Google',
        );
      }

      // Tìm người dùng theo Google ID
      let user: User | null = await this.userRepository.findOne({
        where: { googleId: userInfo.id },
      });

      // Nếu không tìm thấy theo Google ID, thử tìm theo email
      if (!user && userInfo.email) {
        user = await this.userService.findByEmail(userInfo.email);
      }

      if (user) {
        // Người dùng đã tồn tại, cập nhật thông tin Google ID nếu chưa có
        if (!user.googleId) {
          user.googleId = userInfo.id;
          user.updatedAt = Date.now();
          user = await this.userRepository.save(user);
        }

        // Kiểm tra trạng thái tài khoản
        if (!user.isActive) {
          throw new AppException(AUTH_ERROR_CODE.ACCOUNT_LOCKED);
        }

        return user;
      } else {
        // Tạo người dùng mới
        return await this.createUserFromGoogle(userInfo, ref);
      }
    } catch (error) {
      this.logger.error(`Google login failed: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AUTH_ERROR_CODE.SOCIAL_AUTH_FAILED,
        'Đăng nhập Google thất bại',
      );
    }
  }

  /**
   * Xử lý đăng nhập Facebook OAuth2
   * @param code Authorization code từ Facebook
   * @param redirectUri URL chuyển hướng (tùy chọn)
   * @param ref Mã người giới thiệu (tùy chọn)
   * @returns Thông tin người dùng và token
   */
  @Transactional()
  async handleFacebookLogin(
    code: string,
    redirectUri?: string,
    ref?: number,
  ): Promise<User> {
    try {
      const actualRedirectUri = redirectUri || this.facebookRedirectUri;

      this.logger.log(`[DEBUG] Facebook login attempt:`, {
        hasCode: !!code,
        codeLength: code?.length,
        redirectUri: actualRedirectUri,
        hasAppId: !!this.facebookAppId,
        hasAppSecret: !!this.facebookAppSecret,
        appIdPrefix: this.facebookAppId ? `${this.facebookAppId.substring(0, 10)}...` : 'null',
      });

      if (
        !this.facebookAppId ||
        !this.facebookAppSecret ||
        !actualRedirectUri
      ) {
        this.logger.error(`[DEBUG] Missing Facebook configuration:`, {
          hasAppId: !!this.facebookAppId,
          hasAppSecret: !!this.facebookAppSecret,
          hasRedirectUri: !!actualRedirectUri,
        });
        throw new AppException(
          AUTH_ERROR_CODE.SOCIAL_AUTH_FAILED,
          'Thiếu cấu hình Facebook App',
        );
      }

      // Đổi code lấy access token
      const tokenUrl = `https://graph.facebook.com/v18.0/oauth/access_token?client_id=${this.facebookAppId}&redirect_uri=${encodeURIComponent(actualRedirectUri)}&client_secret=${this.facebookAppSecret}&code=${code}`;

      this.logger.log(`[DEBUG] Calling Facebook token API:`, {
        url: tokenUrl.replace(this.facebookAppSecret, '***HIDDEN***').replace(code, `${code.substring(0, 10)}...`),
      });

      const tokenResponse = await lastValueFrom(this.httpService.get(tokenUrl));

      this.logger.log(`[DEBUG] Facebook token response:`, {
        status: tokenResponse.status,
        hasAccessToken: !!tokenResponse.data?.access_token,
        responseKeys: Object.keys(tokenResponse.data || {}),
      });

      const { access_token } = tokenResponse.data;

      if (!access_token) {
        this.logger.error(`[DEBUG] No access token in Facebook response:`, tokenResponse.data);
        throw new AppException(
          AUTH_ERROR_CODE.SOCIAL_AUTH_FAILED,
          'Không thể lấy access token từ Facebook',
        );
      }

      // Lấy thông tin người dùng
      const userInfoUrl = `https://graph.facebook.com/v18.0/me?fields=id,name,email,first_name,last_name,picture&access_token=${access_token}`;
      const userInfoResponse = await lastValueFrom(
        this.httpService.get(userInfoUrl),
      );
      const userInfo: FacebookUserInfo = userInfoResponse.data;

      if (!userInfo || !userInfo.id) {
        throw new AppException(
          AUTH_ERROR_CODE.SOCIAL_AUTH_FAILED,
          'Không thể lấy thông tin người dùng từ Facebook',
        );
      }

      // Tìm người dùng theo Facebook ID
      let user: User | null = await this.userRepository.findOne({
        where: { facebookId: userInfo.id },
      });

      // Nếu không tìm thấy theo Facebook ID, thử tìm theo email
      if (!user && userInfo.email) {
        user = await this.userService.findByEmail(userInfo.email);
      }

      if (user) {
        // Người dùng đã tồn tại, cập nhật thông tin Facebook ID nếu chưa có
        if (!user.facebookId) {
          user.facebookId = userInfo.id;
          user.updatedAt = Date.now();
          user = await this.userRepository.save(user);
        }

        // Kiểm tra trạng thái tài khoản
        if (!user.isActive) {
          throw new AppException(AUTH_ERROR_CODE.ACCOUNT_LOCKED);
        }

        return user;
      } else {
        // Tạo người dùng mới
        return await this.createUserFromFacebook(userInfo, ref);
      }
    } catch (error) {
      this.logger.error(`[DEBUG] Facebook login failed:`, {
        message: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        stack: error.stack,
      });

      // Xử lý lỗi cụ thể từ Facebook API
      if (error.response?.data?.error) {
        const facebookError = error.response.data.error;
        this.logger.error(`[DEBUG] Facebook API Error:`, facebookError);

        if (facebookError.code === 100) {
          throw new AppException(
            AUTH_ERROR_CODE.SOCIAL_AUTH_FAILED,
            `Lỗi xác thực Facebook: ${facebookError.message}`,
          );
        }
      }

      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AUTH_ERROR_CODE.SOCIAL_AUTH_FAILED,
        'Đăng nhập Facebook thất bại',
      );
    }
  }

  /**
   * Tạo người dùng mới từ thông tin Google
   * @param userInfo Thông tin người dùng Google
   * @param ref Mã người giới thiệu (tùy chọn)
   * @returns Người dùng đã tạo
   */
  private async createUserFromGoogle(
    userInfo: GoogleUserInfo,
    ref?: number,
  ): Promise<User> {
    const now = Date.now();
    const newUser = this.userRepository.create({
      email: userInfo.email,
      fullName: userInfo.name,
      googleId: userInfo.id,
      isActive: true,
      isVerifyEmail: true,
      affiliateAccountId: ref, // Ánh xạ ref thành affiliateAccountId
      createdAt: now,
      updatedAt: now,
    });

    const savedUser = await this.userRepository.save(newUser);

    // Gán quyền cho người dùng
    await this.userRoleService.addUserRole(savedUser.id);

    // Tự động tạo RAG API key cho user mới
    await this.ragApiKeyProvisioningService.provisionApiKeyForNewUser(
      savedUser.id,
    );

    // Gửi email chào mừng nếu có email
    if (userInfo.email) {
      try {
        await this.emailPlaceholderService.sendAccountRegistrationSuccess(
          userInfo.email,
          {
            EMAIL: userInfo.email,
            NAME: userInfo.name || userInfo.email,
            USER_EMAIL: userInfo.email,
            USER_ID: savedUser.id.toString(),
          }
        );
      } catch (error) {
        this.logger.error(
          `Failed to send welcome email: ${error.message}`,
          error.stack,
        );
        // Không throw lỗi ở đây để không ảnh hưởng đến luồng đăng nhập
      }
    }

    return savedUser;
  }

  /**
   * Tạo người dùng mới từ thông tin Facebook
   * @param userInfo Thông tin người dùng Facebook
   * @param ref Mã người giới thiệu (tùy chọn)
   * @returns Người dùng đã tạo
   */
  private async createUserFromFacebook(
    userInfo: FacebookUserInfo,
    ref?: number,
  ): Promise<User> {
    const now = Date.now();
    const newUser = this.userRepository.create({
      email: userInfo.email,
      fullName: userInfo.name,
      facebookId: userInfo.id,
      isActive: true,
      isVerifyEmail: !!userInfo.email, // Đánh dấu đã xác thực email nếu có email
      affiliateAccountId: ref, // Ánh xạ ref thành affiliateAccountId
      createdAt: now,
      updatedAt: now,
    });

    const savedUser = await this.userRepository.save(newUser);

    // Gán quyền cho người dùng
    await this.userRoleService.addUserRole(savedUser.id);

    // Tự động tạo RAG API key cho user mới
    await this.ragApiKeyProvisioningService.provisionApiKeyForNewUser(
      savedUser.id,
    );

    // Gửi email chào mừng nếu có email
    if (userInfo.email) {
      try {
        await this.emailPlaceholderService.sendAccountRegistrationSuccess(
          userInfo.email,
          {
            EMAIL: userInfo.email,
            NAME: userInfo.name || userInfo.email,
            USER_EMAIL: userInfo.email,
            USER_ID: savedUser.id.toString(),
          }
        );
      } catch (error) {
        this.logger.error(
          `Failed to send welcome email: ${error.message}`,
          error.stack,
        );
        // Không throw lỗi ở đây để không ảnh hưởng đến luồng đăng nhập
      }
    }

    return savedUser;
  }

  /**
   * Xử lý đăng nhập Zalo OAuth2
   * @param code Authorization code từ Zalo
   * @param redirectUri URL chuyển hướng (tùy chọn)
   * @param ref Mã người giới thiệu (tùy chọn)
   * @param state State token để validation (tùy chọn)
   * @returns Thông tin người dùng và token
   */
  @Transactional()
  async handleZaloLogin(
    code: string,
    redirectUri?: string,
    ref?: number,
    state?: string,
  ): Promise<User> {
    try {
      // Log state để debug (có thể thêm validation logic sau)
      if (state) {
        this.logger.log(`Zalo login with state: ${state}`);
      }

      const zaloAppId = this.configService.get<string>('ZALO_APP_ID');
      const zaloAppSecret = this.configService.get<string>('ZALO_APP_SECRET');
      const actualRedirectUri =
        redirectUri || this.configService.get<string>('ZALO_REDIRECT_URI');

      if (!zaloAppId || !zaloAppSecret || !actualRedirectUri) {
        throw new AppException(
          AUTH_ERROR_CODE.SOCIAL_AUTH_FAILED,
          'Thiếu cấu hình Zalo App',
        );
      }

      // Đổi code lấy access token
      const tokenData = await this.zaloSocialService.getAccessToken(
        zaloAppId,
        zaloAppSecret,
        code,
        actualRedirectUri,
      );

      if (!tokenData.access_token) {
        throw new AppException(
          AUTH_ERROR_CODE.SOCIAL_AUTH_FAILED,
          'Không thể lấy access token từ Zalo',
        );
      }

      // Lấy thông tin người dùng
      const userInfo = await this.zaloSocialService.getUserInfo(
        tokenData.access_token,
      );

      if (!userInfo || !userInfo.id) {
        throw new AppException(
          AUTH_ERROR_CODE.SOCIAL_AUTH_FAILED,
          'Không thể lấy thông tin người dùng từ Zalo',
        );
      }

      // Tìm người dùng theo Zalo ID
      let user: User | null = await this.userRepository.findOne({
        where: { zaloId: userInfo.id },
      });

      if (user) {
        // Người dùng đã tồn tại, kiểm tra trạng thái tài khoản
        if (!user.isActive) {
          throw new AppException(AUTH_ERROR_CODE.ACCOUNT_LOCKED);
        }

        // Cập nhật avatar nếu có
        if (userInfo.picture?.data?.url && !user.avatar) {
          user.avatar = userInfo.picture.data.url;
          user.updatedAt = Date.now();
          user = await this.userRepository.save(user);
        }

        return user;
      } else {
        // Tạo người dùng mới
        return await this.createUserFromZalo(userInfo, ref);
      }
    } catch (error) {
      this.logger.error(`Zalo login failed: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AUTH_ERROR_CODE.SOCIAL_AUTH_FAILED,
        'Đăng nhập Zalo thất bại',
      );
    }
  }

  /**
   * Tạo người dùng mới từ thông tin Zalo
   * @param userInfo Thông tin người dùng Zalo
   * @param ref Mã người giới thiệu (tùy chọn)
   * @returns Người dùng đã tạo
   */
  private async createUserFromZalo(
    userInfo: ZaloUserInfo,
    ref?: number,
  ): Promise<User> {
    const now = Date.now();
    const newUser = this.userRepository.create({
      fullName: userInfo.name,
      zaloId: userInfo.id,
      avatar: userInfo.picture?.data?.url || undefined,
      isActive: true,
      isVerifyEmail: false, // Zalo không cung cấp email
      affiliateAccountId: ref, // Ánh xạ ref thành affiliateAccountId
      createdAt: now,
      updatedAt: now,
    });

    const savedUser = await this.userRepository.save(newUser);

    // Gán quyền cho người dùng
    await this.userRoleService.addUserRole(savedUser.id);

    // Tự động tạo RAG API key cho user mới (Zalo không có email nên dùng tên để tạo tên key)
    await this.ragApiKeyProvisioningService.provisionApiKeyForNewUser(
      savedUser.id,
    );

    this.logger.log(
      `Tạo tài khoản Zalo mới thành công cho user ID: ${savedUser.id}`,
    );

    return savedUser;
  }
}
